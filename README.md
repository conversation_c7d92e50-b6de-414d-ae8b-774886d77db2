这是一个从网页https://www.tiku.com/上获取考试题目的工具。
使用fastapi建立一个后端服务。后端服务会打开一个网页，网页第一行是地址输入，输入网址https://www.tiku.com/question?tree_type=categories&xd=1&chid=2&version_id=141764&grade_id=160574&categories=161087后，
检查网址是否属于tiku.com如果是，打开链接，如果否，给提示后终止
打开链接后，读取考试试题、分类、图片等信息，如果有下一页，自动打开下一页，再读取考试题目。
把读取的考试题目保持到sqlite数据库中，图片存入本地文件夹，并将相对地址存入数据库中。
每读取一页考试题目，在网页上添加显示一次考试题目。

为了完成上述任务，请先用playwright mcp 获取网页代码，分析考试题目所在的位置，及总共页数跟下一页的地址。分析完成后再来开发这个工具。

请先分析我的需求，制定详细的任务清单，每次回答都需要基于任务清单一步一步完成