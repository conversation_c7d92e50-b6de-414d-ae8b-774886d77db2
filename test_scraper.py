#!/usr/bin/env python3
import asyncio
import sys
import os
sys.path.append('src')

from scraper import scrape_questions_stream

async def test_scraper():
    url = "https://www.tiku.com/question?tree_type=categories&xd=1&chid=2&version_id=141764&grade_id=160574&categories=161087"
    print(f"开始测试爬取: {url}")
    
    count = 0
    try:
        async for question in scrape_questions_stream(url):
            count += 1
            print(f"\n=== 题目 {count} ===")
            print(f"标题: {question['title'][:100]}...")
            print(f"类别: {question['category']}")
            print(f"难度: {question['difficulty']}")
            print(f"使用次数: {question['usage_count']}")
            print(f"更新时间: {question['update_time']}")
            print(f"内容长度: {len(question['content'])} 字符")
            
            # 只测试前3个题目
            if count >= 3:
                break
                
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n总共获取到 {count} 个题目")

if __name__ == "__main__":
    asyncio.run(test_scraper())
