from playwright.async_api import async_playwright
import re
import requests
import os

async def scrape_questions(url):
    questions = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        # 提取年级和学科信息
        grade = ""
        subject = ""

        # 从页面标题提取学科信息
        try:
            title_elem = await page.query_selector("title")
            if title_elem:
                title_text = await title_elem.inner_text()
                if "小学语文" in title_text:
                    subject = "语文"
                elif "小学数学" in title_text:
                    subject = "数学"
                elif "小学英语" in title_text:
                    subject = "英语"
                # 可以继续添加其他学科
        except:
            pass

        # 从导航路径提取年级信息
        try:
            breadcrumb_links = await page.query_selector_all("a")
            for link in breadcrumb_links:
                link_text = await link.inner_text()
                if "年级" in link_text:
                    grade = link_text.strip()
                    break
        except:
            pass

        # 如果没有找到，尝试从URL参数提取
        if not grade or not subject:
            # 从URL中提取参数
            if "xd=1" in url:  # 小学
                if "chid=2" in url:  # 语文
                    subject = "语文"
                elif "chid=3" in url:  # 数学
                    subject = "数学"
                elif "chid=4" in url:  # 英语
                    subject = "英语"

        print(f"提取到的信息 - 年级: {grade}, 学科: {subject}")

        # 滚动页面以加载更多内容
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(2000)

        # 等待页面加载完成
        await page.wait_for_selector("ul", timeout=10000)

        # 获取试题列表 - 查找包含题目的列表项
        question_elements = await page.query_selector_all("ul li")
        print(f"找到 {len(question_elements)} 个列表项")

        valid_questions = 0
        for i, elem in enumerate(question_elements):
            # 获取题目文本内容
            full_text = await elem.inner_text()
            if not full_text or len(full_text.strip()) < 20:
                continue

            print(f"列表项 {i+1}: {full_text[:100]}...")

            # 检查是否包含题目编号（如 "1." "2." 等），但排除子问题
            lines = full_text.strip().split('\n')

            # 查找以数字开头的行（题目行）
            question_line = None
            question_line_index = -1
            for idx, line in enumerate(lines):
                line = line.strip()
                if re.search(r'^\s*\d+\.\s+', line) and not re.search(r'^\s*\(\s*\d+\s*\)', line):
                    question_line = line
                    question_line_index = idx
                    break

            if not question_line:
                continue

            valid_questions += 1
            print(f"找到有效题目 {valid_questions}: {question_line[:50]}...")

            # 提取题目标题和内容
            title = question_line.strip()
            content = full_text.strip()

            # 检查内容是否为空或过短（可能是子问题）
            if not content or len(content.strip()) < 20:
                continue

            # 从页面结构中提取题目类型、难度等信息
            category = ""
            difficulty = ""
            usage_count = ""
            update_time = ""

            # 尝试从题目元素中提取元信息
            meta_info = await elem.query_selector("div:first-child")
            if meta_info:
                meta_text = await meta_info.inner_text()
                # 解析类型、难度、组卷次数等信息
                if "|" in meta_text:
                    parts = meta_text.split("|")
                    if len(parts) >= 3:
                        category = parts[0].strip()
                        difficulty = parts[1].strip()
                        usage_count = parts[2].strip()

            # 尝试提取更新时间 - 从完整文本中搜索
            time_match = re.search(r'更新时间：(\d{4}-\d{2}-\d{2})', full_text)
            if time_match:
                update_time = time_match.group(1)

            # 如果没有找到类别信息，设置默认值
            if not category:
                category = "未知类型"
            if not difficulty:
                difficulty = "普通"
            if not usage_count:
                usage_count = "0次"

            # 设置知识点（暂时为空，后续可以从页面提取）
            knowledge_points = ""

            # 检查是否有图片并下载
            image_path = ""
            img_elem = await elem.query_selector("img")
            if img_elem:
                img_url = await img_elem.get_attribute("src")
                if img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https:' + img_url
                    img_response = requests.get(img_url)
                    if img_response.status_code == 200:
                        img_filename = os.path.basename(img_url.split("?")[0])
                        img_path = f"images/{img_filename}"
                        with open(img_path, "wb") as f:
                            f.write(img_response.content)
                        image_path = img_path

            questions.append({
                "grade": grade,
                "subject": subject,
                "category": category,
                "usage_count": usage_count,
                "title": title,
                "content": content,
                "answer": ""
            })
        
        # 检查是否有下一页
        next_page_elem = await page.query_selector(".JV_page a[data-page='2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                next_questions = await scrape_questions(next_page_url)
                questions.extend(next_questions)
        
        await browser.close()
    
    return questions

async def scrape_questions_stream(url):
    """流式获取题目，每获取一个就yield一个"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        # 提取年级和学科信息
        grade = ""
        subject = ""

        # 从页面标题提取学科信息
        try:
            title_elem = await page.query_selector("title")
            if title_elem:
                title_text = await title_elem.inner_text()
                if "小学语文" in title_text:
                    subject = "语文"
                elif "小学数学" in title_text:
                    subject = "数学"
                elif "小学英语" in title_text:
                    subject = "英语"
        except:
            pass

        # 从导航路径提取年级信息
        try:
            breadcrumb_links = await page.query_selector_all("a")
            for link in breadcrumb_links:
                link_text = await link.inner_text()
                if "年级" in link_text:
                    grade = link_text.strip()
                    break
        except:
            pass

        # 如果没有找到，尝试从URL参数提取
        if not grade or not subject:
            if "xd=1" in url:  # 小学
                if "chid=2" in url:  # 语文
                    subject = "语文"
                elif "chid=3" in url:  # 数学
                    subject = "数学"
                elif "chid=4" in url:  # 英语
                    subject = "英语"

        print(f"流式提取到的信息 - 年级: {grade}, 学科: {subject}")

        # 滚动页面以加载更多内容
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(2000)

        # 等待页面加载完成
        await page.wait_for_selector("ul", timeout=10000)

        # 获取试题列表 - 查找包含题目的列表项
        question_elements = await page.query_selector_all("ul li")
        print(f"流式函数找到 {len(question_elements)} 个列表项")

        valid_questions = 0
        for i, elem in enumerate(question_elements):
            # 获取题目文本内容
            full_text = await elem.inner_text()
            if not full_text or len(full_text.strip()) < 20:
                continue

            print(f"流式列表项 {i+1}: {full_text[:100]}...")

            # 检查是否包含题目编号（如 "1." "2." 等），但排除子问题
            lines = full_text.strip().split('\n')

            # 查找以数字开头的行（题目行）
            question_line = None
            question_line_index = -1
            for idx, line in enumerate(lines):
                line = line.strip()
                if re.search(r'^\s*\d+\.\s+', line) and not re.search(r'^\s*\(\s*\d+\s*\)', line):
                    question_line = line
                    question_line_index = idx
                    break

            if not question_line:
                continue

            valid_questions += 1
            print(f"流式找到有效题目 {valid_questions}: {question_line[:50]}...")

            # 提取题目标题和内容
            title = question_line.strip()
            content = full_text.strip()

            # 检查内容是否为空或过短（可能是子问题）
            if not content or len(content.strip()) < 20:
                continue

            # 从页面结构中提取题目类型、难度等信息
            category = ""
            difficulty = ""
            usage_count = ""
            update_time = ""

            # 尝试从题目元素中提取元信息
            meta_info = await elem.query_selector("div:first-child")
            if meta_info:
                meta_text = await meta_info.inner_text()
                # 解析类型、难度、组卷次数等信息
                if "|" in meta_text:
                    parts = meta_text.split("|")
                    if len(parts) >= 3:
                        category = parts[0].strip()
                        difficulty = parts[1].strip()
                        usage_count = parts[2].strip()

            # 尝试提取更新时间 - 从完整文本中搜索
            time_match = re.search(r'更新时间：(\d{4}-\d{2}-\d{2})', full_text)
            if time_match:
                update_time = time_match.group(1)

            # 如果没有找到类别信息，设置默认值
            if not category:
                category = "未知类型"
            if not difficulty:
                difficulty = "普通"
            if not usage_count:
                usage_count = "0次"

            # 设置知识点（暂时为空，后续可以从页面提取）
            knowledge_points = ""

            # 检查是否有图片并下载
            image_path = ""
            img_elem = await elem.query_selector("img")
            if img_elem:
                img_url = await img_elem.get_attribute("src")
                if img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https:' + img_url
                    img_response = requests.get(img_url)
                    if img_response.status_code == 200:
                        img_filename = os.path.basename(img_url.split("?")[0])
                        img_path = f"images/{img_filename}"
                        with open(img_path, "wb") as f:
                            f.write(img_response.content)
                        image_path = img_path

            question = {
                "grade": grade,
                "subject": subject,
                "category": category,
                "usage_count": usage_count,
                "title": title,
                "content": content,
                "answer": ""
            }

            # 立即yield这个题目
            yield question

        # 检查是否有下一页
        next_page_elem = await page.query_selector(".JV_page a[data-page='2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                # 递归处理下一页
                async for question in scrape_questions_stream(next_page_url):
                    yield question

        await browser.close()
