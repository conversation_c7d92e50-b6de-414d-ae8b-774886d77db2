from playwright.async_api import async_playwright
import re
import requests
import os

async def scrape_questions(url):
    questions = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")
        
        # 滚动页面以加载更多内容
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(2000)
        
        # 获取试题列表
        question_elements = await page.query_selector_all(".JV_list .QuestionView")
        for elem in question_elements:
            title_elem = await elem.query_selector(".q-tit")
            title = (await title_elem.inner_text()).strip() if title_elem else ""

            # 过滤掉子问题：检查标题是否包含子问题标识
            if not title or re.search(r'^\s*\(\s*\d+\s*\)', title) or re.search(r'^\s*\d+\s*[\.、]', title):
                continue

            content_elem = await elem.query_selector(".q-mc")
            content = (await content_elem.inner_html()).strip() if content_elem else ""

            # 检查内容是否为空或过短（可能是子问题）
            if not content or len(content.strip()) < 10:
                continue

            category_elem = await elem.query_selector(".q-attr span:nth-child(1)")
            category = (await category_elem.inner_text()).strip() if category_elem else ""
            difficulty_elem = await elem.query_selector(".q-attr span:nth-child(3)")
            difficulty = (await difficulty_elem.inner_text()).strip() if difficulty_elem else ""
            usage_count_elem = await elem.query_selector(".q-attr span:nth-child(5)")
            usage_count = (await usage_count_elem.inner_text()).strip() if usage_count_elem else ""
            knowledge_points_elem = await elem.query_selector(".q-know-list")
            knowledge_points = (await knowledge_points_elem.inner_text()).strip() if knowledge_points_elem else ""
            update_time_elem = await elem.query_selector(".q-attr.fl span")
            update_time = (await update_time_elem.inner_text()).strip() if update_time_elem else ""

            # 进一步验证是否为有效题目：必须有类别信息
            if not category:
                continue
            
            # 检查是否有图片并下载
            image_path = ""
            img_elem = await elem.query_selector("img")
            if img_elem:
                img_url = await img_elem.get_attribute("src")
                if img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https:' + img_url
                    img_response = requests.get(img_url)
                    if img_response.status_code == 200:
                        img_filename = os.path.basename(img_url.split("?")[0])
                        img_path = f"images/{img_filename}"
                        with open(img_path, "wb") as f:
                            f.write(img_response.content)
                        image_path = img_path
            
            questions.append({
                "title": title,
                "content": content,
                "category": category,
                "difficulty": difficulty,
                "usage_count": usage_count,
                "knowledge_points": knowledge_points,
                "image_path": image_path,
                "update_time": update_time
            })
        
        # 检查是否有下一页
        next_page_elem = await page.query_selector(".JV_page a[data-page='2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                next_questions = await scrape_questions(next_page_url)
                questions.extend(next_questions)
        
        await browser.close()
    
    return questions

async def scrape_questions_stream(url):
    """流式获取题目，每获取一个就yield一个"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        # 滚动页面以加载更多内容
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(2000)

        # 获取试题列表
        question_elements = await page.query_selector_all(".JV_list .QuestionView")
        for elem in question_elements:
            title_elem = await elem.query_selector(".q-tit")
            title = (await title_elem.inner_text()).strip() if title_elem else ""

            # 过滤掉子问题：检查标题是否包含子问题标识
            if not title or re.search(r'^\s*\(\s*\d+\s*\)', title) or re.search(r'^\s*\d+\s*[\.、]', title):
                continue

            content_elem = await elem.query_selector(".q-mc")
            content = (await content_elem.inner_html()).strip() if content_elem else ""

            # 检查内容是否为空或过短（可能是子问题）
            if not content or len(content.strip()) < 10:
                continue

            category_elem = await elem.query_selector(".q-attr span:nth-child(1)")
            category = (await category_elem.inner_text()).strip() if category_elem else ""
            difficulty_elem = await elem.query_selector(".q-attr span:nth-child(3)")
            difficulty = (await difficulty_elem.inner_text()).strip() if difficulty_elem else ""
            usage_count_elem = await elem.query_selector(".q-attr span:nth-child(5)")
            usage_count = (await usage_count_elem.inner_text()).strip() if usage_count_elem else ""
            knowledge_points_elem = await elem.query_selector(".q-know-list")
            knowledge_points = (await knowledge_points_elem.inner_text()).strip() if knowledge_points_elem else ""
            update_time_elem = await elem.query_selector(".q-attr.fl span")
            update_time = (await update_time_elem.inner_text()).strip() if update_time_elem else ""

            # 进一步验证是否为有效题目：必须有类别信息
            if not category:
                continue

            # 检查是否有图片并下载
            image_path = ""
            img_elem = await elem.query_selector("img")
            if img_elem:
                img_url = await img_elem.get_attribute("src")
                if img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https:' + img_url
                    img_response = requests.get(img_url)
                    if img_response.status_code == 200:
                        img_filename = os.path.basename(img_url.split("?")[0])
                        img_path = f"images/{img_filename}"
                        with open(img_path, "wb") as f:
                            f.write(img_response.content)
                        image_path = img_path

            question = {
                "title": title,
                "content": content,
                "category": category,
                "difficulty": difficulty,
                "usage_count": usage_count,
                "knowledge_points": knowledge_points,
                "image_path": image_path,
                "update_time": update_time
            }

            # 立即yield这个题目
            yield question

        # 检查是否有下一页
        next_page_elem = await page.query_selector(".JV_page a[data-page='2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                # 递归处理下一页
                async for question in scrape_questions_stream(next_page_url):
                    yield question

        await browser.close()
