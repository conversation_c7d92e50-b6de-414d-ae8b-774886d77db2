from playwright.async_api import async_playwright
import re
import os
import asyncio

async def download_image_with_playwright(page, img_url):
    """使用Playwright下载图片"""
    try:
        # 创建images目录
        os.makedirs("images", exist_ok=True)

        # 获取图片文件名
        img_filename = os.path.basename(img_url.split("?")[0])
        if not img_filename or '.' not in img_filename:
            img_filename = f"image_{hash(img_url)}.jpg"

        img_path = f"images/{img_filename}"

        # 使用Playwright下载图片
        response = await page.request.get(img_url)
        if response.status == 200:
            with open(img_path, "wb") as f:
                f.write(await response.body())
            return img_path
    except Exception as e:
        print(f"下载图片失败: {e}")
    return ""

async def scrape_questions(url):
    questions = []
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        # 等待页面完全加载
        await page.wait_for_timeout(3000)

        # 提取年级和学科信息
        grade = ""
        subject = ""

        # 从页面标题提取学科信息
        try:
            title_elem = await page.query_selector("title")
            if title_elem:
                title_text = await title_elem.inner_text()
                if "小学语文" in title_text:
                    subject = "语文"
                elif "小学数学" in title_text:
                    subject = "数学"
                elif "小学英语" in title_text:
                    subject = "英语"
        except:
            pass

        # 从导航路径提取年级信息
        try:
            breadcrumb_elem = await page.query_selector("a[href*='grade_id']")
            if breadcrumb_elem:
                grade_text = await breadcrumb_elem.inner_text()
                grade = grade_text.strip()
        except:
            pass

        # 如果没有找到，尝试从URL参数提取
        if not grade or not subject:
            if "xd=1" in url:  # 小学
                if "chid=2" in url:  # 语文
                    subject = "语文"
                elif "chid=3" in url:  # 数学
                    subject = "数学"
                elif "chid=4" in url:  # 英语
                    subject = "英语"

        print(f"提取到的信息 - 年级: {grade}, 学科: {subject}")

        # 等待题目列表加载
        await page.wait_for_selector("ul li", timeout=10000)

        # 获取题目列表 - 基于真实页面结构
        question_elements = await page.query_selector_all("ul li")
        print(f"找到 {len(question_elements)} 个列表项")

        valid_questions = 0
        for i, elem in enumerate(question_elements):
            try:
                # 检查是否包含题目标签信息（题型|难度|组卷次数）
                meta_elem = await elem.query_selector("div:first-child")
                if not meta_elem:
                    continue

                meta_text = await meta_elem.inner_text()
                if "|" not in meta_text:
                    continue

                # 解析题目元信息
                parts = meta_text.split("|")
                if len(parts) < 3:
                    continue

                category = parts[0].strip()
                difficulty = parts[1].strip()
                usage_count = parts[2].strip()

                # 获取题目内容
                content_elem = await elem.query_selector("div:nth-child(2)")
                if not content_elem:
                    continue

                content = await content_elem.inner_text()
                if not content or len(content.strip()) < 10:
                    continue

                # 提取题目标题（第一行）
                lines = content.strip().split('\n')
                title = lines[0].strip() if lines else content[:100]

                valid_questions += 1
                print(f"找到有效题目 {valid_questions}: {title[:50]}...")

                # 处理图片（如果有）
                img_path = ""
                img_elem = await elem.query_selector("img")
                if img_elem:
                    img_url = await img_elem.get_attribute("src")
                    if img_url:
                        if not img_url.startswith(('http://', 'https://')):
                            img_url = 'https:' + img_url
                        # 使用Playwright下载图片
                        img_path = await download_image_with_playwright(page, img_url)

                # 获取完整元素文本用于提取更新时间
                full_text = await elem.inner_text()

                # 尝试提取更新时间
                update_time = ""
                time_match = re.search(r'更新时间：(\d{4}-\d{2}-\d{2})', full_text)
                if time_match:
                    update_time = time_match.group(1)

                # 如果没有找到类别信息，设置默认值
                if not category:
                    category = "未知类型"
                if not difficulty:
                    difficulty = "普通"
                if not usage_count:
                    usage_count = "0次"

                questions.append({
                    "grade": grade,
                    "subject": subject,
                    "category": category,
                    "difficulty": difficulty,
                    "usage_count": usage_count,
                    "title": title,
                    "content": content,
                    "answer": "",  # 空的答案字段
                    "image_path": img_path
                })

            except Exception as e:
                print(f"处理题目时出错: {e}")
                continue
        
        # 检查是否有下一页
        next_page_elem = await page.query_selector(".JV_page a[data-page='2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                next_questions = await scrape_questions(next_page_url)
                questions.extend(next_questions)
        
        await browser.close()
    
    return questions

async def scrape_questions_stream(url):
    """流式获取题目，每获取一个就yield一个"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        # 提取年级和学科信息
        grade = ""
        subject = ""

        # 从页面标题提取学科信息
        try:
            title_elem = await page.query_selector("title")
            if title_elem:
                title_text = await title_elem.inner_text()
                if "小学语文" in title_text:
                    subject = "语文"
                elif "小学数学" in title_text:
                    subject = "数学"
                elif "小学英语" in title_text:
                    subject = "英语"
        except:
            pass

        # 尝试从页面中获取更详细的年级信息
        try:
            # 查找版本信息元素
            version_elem = await page.query_selector("div[class*='version'], .grade-info")
            if version_elem:
                version_text = await version_elem.inner_text()
                if "年级" in version_text:
                    grade = version_text.strip()
        except:
            pass

        # 如果没有找到，尝试从URL参数提取
        if not grade or not subject:
            if "xd=1" in url:  # 小学
                if "chid=2" in url:  # 语文
                    subject = "语文"
                elif "chid=3" in url:  # 数学
                    subject = "数学"
                elif "chid=4" in url:  # 英语
                    subject = "英语"

        print(f"流式提取到的信息 - 年级: {grade}, 学科: {subject}")

        # 等待题目列表加载完成
        await page.wait_for_selector("ul li", timeout=10000)

        # 获取题目列表 - 基于真实页面结构
        question_elements = await page.query_selector_all("ul li")
        print(f"流式函数找到 {len(question_elements)} 个列表项")

        valid_questions = 0
        for i, elem in enumerate(question_elements):
            # 获取题目文本内容
            full_text = await elem.inner_text()
            if not full_text or len(full_text.strip()) < 20:
                continue

            print(f"流式列表项 {i}: {full_text[:100]}...")

            # 检查是否是真正的题目（包含题目编号）
            if not re.search(r'\d+\.\s+', full_text):
                continue

            # 跳过纯章节标题或导航内容
            if any(keyword in full_text for keyword in ["第一单元", "本单元复习", "本课综合", "更新时间"]):
                continue

            valid_questions += 1
            print(f"流式找到有效题目 {valid_questions}: {full_text[:100]}...")

            # 提取题目内容
            content = full_text.strip()

            # 简单的题型识别
            category = "未知类型"
            if "选择" in content or "（ ）" in content or "（    ）" in content:
                category = "单选题"
            elif "填空" in content or "______" in content or "____" in content:
                category = "填空题"
            elif "阅读" in content:
                category = "现代文阅读"
            elif "写作" in content or "作文" in content:
                category = "写作题"
            elif "古诗" in content or "诗词" in content:
                category = "古诗词"
            elif "课内" in content:
                category = "课内阅读"
            elif "课外" in content:
                category = "课外阅读"
            elif "语言表达" in content:
                category = "语言表达"

            # 设置默认值
            difficulty = "普通"
            usage_count = "0次"

            # 提取题目标题（第一行）
            lines = content.strip().split('\n')
            title = lines[0].strip() if lines else content[:100]

            # 检查是否有图片并下载
            image_path = ""
            img_elem = await elem.query_selector("img")
            if img_elem:
                img_url = await img_elem.get_attribute("src")
                if img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https://www.tiku.com' + img_url
                    # 使用Playwright下载图片
                    image_path = await download_image_with_playwright(page, img_url)

            question = {
                "grade": grade,
                "subject": subject,
                "category": category,
                "difficulty": difficulty,
                "usage_count": usage_count,
                "title": title,
                "content": content,
                "answer": ""
            }

            # 立即yield这个题目
            yield question

        # 检查是否有下一页
        next_page_elem = await page.query_selector("a[href*='page=2']")
        if next_page_elem:
            next_page_url = await next_page_elem.get_attribute("href")
            if next_page_url and 'javascript:;' not in next_page_url:
                if not next_page_url.startswith(('http://', 'https://')):
                    next_page_url = 'https://www.tiku.com' + next_page_url
                # 递归处理下一页
                async for question in scrape_questions_stream(next_page_url):
                    yield question

        await browser.close()
