from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from scraper import scrape_questions
import sqlite3
import os

app = FastAPI()
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化数据库
def init_db():
    conn = sqlite3.connect('questions.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            content TEXT,
            category TEXT,
            difficulty TEXT,
            usage_count TEXT,
            knowledge_points TEXT,
            image_path TEXT,
            update_time TEXT
        )
    ''')
    conn.commit()
    conn.close()

@app.on_event("startup")
async def startup_db_client():
    init_db()
    if not os.path.exists('images'):
        os.makedirs('images')

@app.get("/", response_class=HTMLResponse)
async def get_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "questions": []})

@app.post("/scrape", response_class=HTMLResponse)
async def scrape_url(request: Request, url: str = Form(...)):
    if "tiku.com" not in url:
        return templates.TemplateResponse("index.html", {"request": request, "error": "网址必须属于 tiku.com 域名", "questions": []})
    
    questions = await scrape_questions(url)
    conn = sqlite3.connect('questions.db')
    cursor = conn.cursor()
    for q in questions:
        cursor.execute('''
            INSERT INTO questions (title, content, category, difficulty, usage_count, knowledge_points, image_path, update_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (q['title'], q['content'], q['category'], q['difficulty'], q['usage_count'], q['knowledge_points'], q['image_path'], q['update_time']))
    conn.commit()
    conn.close()
    
    return templates.TemplateResponse("index.html", {"request": request, "questions": questions})
