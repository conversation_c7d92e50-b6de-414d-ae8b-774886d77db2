from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from scraper import scrape_questions_stream
import sqlite3
import os
import json
import asyncio
from urllib.parse import unquote

app = FastAPI()
templates = Jinja2Templates(directory="src/templates")
app.mount("/static", StaticFiles(directory="src/static"), name="static")

# 全局停止标志
stop_scraping = False

# 初始化数据库
def init_db():
    # 确保db目录存在
    if not os.path.exists('db'):
        os.makedirs('db')
    conn = sqlite3.connect('db/questions.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            grade TEXT,
            subject TEXT,
            category TEXT,
            usage_count TEXT,
            title TEXT,
            content TEXT,
            answer TEXT DEFAULT ''
        )
    ''')
    conn.commit()
    conn.close()

@app.on_event("startup")
async def startup_db_client():
    init_db()
    if not os.path.exists('images'):
        os.makedirs('images')

@app.get("/", response_class=HTMLResponse)
async def get_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "questions": []})

@app.post("/scrape", response_class=HTMLResponse)
async def scrape_url(request: Request, url: str = Form(...)):
    if "tiku.com" not in url:
        return templates.TemplateResponse("index.html", {"request": request, "error": "网址必须属于 tiku.com 域名", "questions": []})

    # 返回流式页面
    return templates.TemplateResponse("stream.html", {"request": request, "url": url})

@app.get("/stream/{url:path}")
async def stream_questions(url: str):
    """流式获取题目"""
    # URL解码
    url = unquote(url)
    if "tiku.com" not in url:
        return StreamingResponse(
            iter([f"data: {json.dumps({'error': '网址必须属于 tiku.com 域名'})}\n\n"]),
            media_type="text/plain"
        )

    async def generate():
        conn = sqlite3.connect('db/questions.db')
        cursor = conn.cursor()

        try:
            async for question in scrape_questions_stream(url):
                # 检查是否需要停止
                global stop_scraping
                if stop_scraping:
                    yield f"data: {json.dumps({'status': 'paused', 'message': '爬取已暂停'})}\n\n"
                    # 等待继续信号
                    while stop_scraping:
                        await asyncio.sleep(1)
                    yield f"data: {json.dumps({'status': 'resumed', 'message': '爬取已恢复'})}\n\n"

                # 保存到数据库
                cursor.execute('''
                    INSERT INTO questions (grade, subject, category, usage_count, title, content, answer)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (question['grade'], question['subject'], question['category'], question['usage_count'],
                      question['title'], question['content'], question['answer']))
                conn.commit()

                # 发送给前端
                yield f"data: {json.dumps(question)}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
        finally:
            conn.close()
            yield f"data: {json.dumps({'done': True})}\n\n"

    return StreamingResponse(generate(), media_type="text/event-stream")

@app.post("/stop")
async def stop_scraping_endpoint():
    """停止爬取"""
    global stop_scraping
    stop_scraping = True
    return {"status": "stopped"}

@app.post("/continue")
async def continue_scraping_endpoint():
    """继续爬取"""
    global stop_scraping
    stop_scraping = False
    return {"status": "continued"}

@app.get("/status")
async def get_status():
    """获取当前状态"""
    global stop_scraping
    return {"is_stopped": stop_scraping}

if __name__ == "__main__":
    import uvicorn
    print("Starting server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
