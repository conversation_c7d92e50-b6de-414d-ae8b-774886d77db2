from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from scraper import scrape_questions_stream
import sqlite3
import os
import json
import asyncio
from urllib.parse import unquote

app = FastAPI()
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="src/static"), name="static")

# 初始化数据库
def init_db():
    # 确保db目录存在
    if not os.path.exists('db'):
        os.makedirs('db')
    conn = sqlite3.connect('db/questions.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            content TEXT,
            category TEXT,
            difficulty TEXT,
            usage_count TEXT,
            knowledge_points TEXT,
            image_path TEXT,
            update_time TEXT
        )
    ''')
    conn.commit()
    conn.close()

@app.on_event("startup")
async def startup_db_client():
    init_db()
    if not os.path.exists('images'):
        os.makedirs('images')

@app.get("/", response_class=HTMLResponse)
async def get_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "questions": []})

@app.post("/scrape", response_class=HTMLResponse)
async def scrape_url(request: Request, url: str = Form(...)):
    if "tiku.com" not in url:
        return templates.TemplateResponse("index.html", {"request": request, "error": "网址必须属于 tiku.com 域名", "questions": []})

    # 返回流式页面
    return templates.TemplateResponse("stream.html", {"request": request, "url": url})

@app.get("/stream/{url:path}")
async def stream_questions(url: str):
    """流式获取题目"""
    # URL解码
    url = unquote(url)
    if "tiku.com" not in url:
        return StreamingResponse(
            iter([f"data: {json.dumps({'error': '网址必须属于 tiku.com 域名'})}\n\n"]),
            media_type="text/plain"
        )

    async def generate():
        conn = sqlite3.connect('db/questions.db')
        cursor = conn.cursor()

        try:
            async for question in scrape_questions_stream(url):
                # 保存到数据库
                cursor.execute('''
                    INSERT INTO questions (title, content, category, difficulty, usage_count, knowledge_points, image_path, update_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (question['title'], question['content'], question['category'], question['difficulty'],
                      question['usage_count'], question['knowledge_points'], question['image_path'], question['update_time']))
                conn.commit()

                # 发送给前端
                yield f"data: {json.dumps(question)}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
        finally:
            conn.close()
            yield f"data: {json.dumps({'done': True})}\n\n"

    return StreamingResponse(generate(), media_type="text/event-stream")

if __name__ == "__main__":
    import uvicorn
    print("Starting server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
