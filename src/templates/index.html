<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库网试题获取工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .input-section {
            margin-bottom: 20px;
        }
        .input-section input[type="text"] {
            width: 80%;
            padding: 8px;
            margin-right: 10px;
        }
        .input-section button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .input-section button:hover {
            background-color: #45a049;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
        .questions-list {
            margin-top: 20px;
        }
        .question {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .question h3 {
            margin-top: 0;
        }
        .question img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>题库网试题获取工具</h1>
        <div class="input-section">
            <form method="post" action="/scrape">
                <input type="text" name="url" placeholder="请输入题库网网址" required>
                <button type="submit">获取试题</button>
            </form>
            {% if error %}
            <p class="error">{{ error }}</p>
            {% endif %}
        </div>
        <div class="questions-list">
            {% if questions %}
                {% for question in questions %}
                <div class="question">
                    <h3>{{ question.title }}</h3>
                    <div>{{ question.content | safe }}</div>
                    <p><strong>题型：</strong> {{ question.category }}</p>
                    <p><strong>难度：</strong> {{ question.difficulty }}</p>
                    <p><strong>组卷次数：</strong> {{ question.usage_count }}</p>
                    <p><strong>知识点：</strong> {{ question.knowledge_points }}</p>
                    {% if question.image_path %}
                    <img src="/static/{{ question.image_path }}" alt="试题图片">
                    {% endif %}
                    <p><strong>更新时间：</strong> {{ question.update_time }}</p>
                </div>
                {% endfor %}
            {% else %}
                <p>暂无试题数据，请输入网址后获取。</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
